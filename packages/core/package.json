{"name": "@google/gemini-cli-core", "version": "0.1.5", "description": "Gemini CLI Server", "repository": "google-gemini/gemini-cli", "type": "module", "main": "dist/index.js", "scripts": {"start": "node dist/src/index.js", "build": "node ../../scripts/build_package.js", "clean": "rm -rf dist", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "test": "vitest run", "test:ci": "vitest run --coverage", "typecheck": "tsc --noEmit", "prerelease:version": "node ../../scripts/bind_package_version.js", "prerelease:deps": "node ../../scripts/bind_package_dependencies.js", "prepack": "npm run build", "prepublishOnly": "node ../../scripts/prepublish.js"}, "files": ["dist"], "dependencies": {"@google/genai": "^1.4.0", "@modelcontextprotocol/sdk": "^1.11.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-logs-otlp-grpc": "^0.52.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.52.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.52.0", "@opentelemetry/instrumentation-http": "^0.52.0", "@opentelemetry/sdk-node": "^0.52.0", "@types/glob": "^8.1.0", "@types/html-to-text": "^9.0.4", "diff": "^7.0.0", "dotenv": "^16.4.7", "glob": "^10.4.5", "google-auth-library": "^9.11.0", "html-to-text": "^9.0.5", "ignore": "^7.0.0", "micromatch": "^4.0.8", "open": "^10.1.2", "shell-quote": "^1.8.2", "simple-git": "^3.28.0", "strip-ansi": "^7.1.0", "undici": "^7.10.0", "ws": "^8.18.0"}, "devDependencies": {"@types/diff": "^7.0.2", "@types/dotenv": "^6.1.1", "@types/micromatch": "^4.0.8", "@types/minimatch": "^5.1.2", "@types/ws": "^8.5.10", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "engines": {"node": ">=18"}}